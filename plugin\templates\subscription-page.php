<?php
/**
 * Modern Subscription Page Template - Alandal Style
 * This can be used as a shortcode or standalone page
 */

// Get current user subscription status
$user_id = get_current_user_id();
$current_subscription = null;
$has_active_subscription = false;

if ($user_id && class_exists('WP_MANGA_SUBSCRIPTION_MANAGER')) {
    $subscription_manager = WP_MANGA_SUBSCRIPTION_MANAGER::get_instance();
    $current_subscription = $subscription_manager->get_user_subscription($user_id);
    $has_active_subscription = $current_subscription && $current_subscription->status === 'active';
}

// Get subscription plans for display
global $wpdb;
$plans_table = $wpdb->prefix . 'manga_subscription_plans';
$plans = $wpdb->get_results("SELECT * FROM $plans_table WHERE is_active = 1 ORDER BY duration_months ASC");

// Get some popular manga for display
$popular_manga = get_posts(array(
    'post_type' => 'wp-manga',
    'posts_per_page' => 6,
    'meta_key' => '_wp_manga_views',
    'orderby' => 'meta_value_num',
    'order' => 'DESC'
));
?>

<div class="ultra-modern-subscription">
    <!-- Particle Background -->
    <div class="particle-bg">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <!-- Animated Background Elements -->
        <div class="bg-elements">
            <div class="pattern-image pattern-1"></div>
            <div class="pattern-image pattern-2"></div>
            <div class="grid-overlay"></div>
        </div>

        <div class="hero-container">

            <div class="hero-content">
                <!-- Brand Badge -->
                <div class="brand-badge">
                    <span class="brand-icon">⚡</span>
                    <!-- <span class="brand-text"><?php echo get_bloginfo('name'); ?></span> -->
                    <span class="brand-text">Raijin Plus</span>
                    <span class="brand-plus">PREMIUM</span>
                </div>

                <!-- Main Title -->
                <h1 class="hero-title">
                    <span class="title-line-1">UNLOCK THE</span>
                    <span class="title-line-2">
                        <span class="gradient-word">ULTIMATE</span>
                        <span class="title-word">MANGA</span>
                    </span>
                    <span class="title-line-3">EXPERIENCE</span>
                </h1>

                <!-- Subtitle -->
                <p class="hero-subtitle">
                    Join thousands of readers accessing premium content, early releases,
                    and exclusive chapters from the world's best manga creators.
                </p>

                <!-- Stats -->
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">Premium Chapters</span>
                    </div>
                    <div class="stat-divider"></div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Exclusive Series</span>
                    </div>
                    <div class="stat-divider"></div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Access</span>
                    </div>
                </div>

                <!-- CTA Section -->
                <?php if ($has_active_subscription): ?>
                    <div class="premium-member-display">
                        <div class="member-crown">👑</div>
                        <div class="member-info">
                            <h3>Welcome Back, Premium Member!</h3>
                            <p><?php echo esc_html($current_subscription->plan_name); ?> • Expires <?php echo date('M j, Y', strtotime($current_subscription->end_date)); ?></p>
                        </div>
                        <button class="manage-btn" onclick="openSubscriptionModal()">
                            Manage Subscription
                        </button>
                    </div>
                <?php else: ?>
                    <div class="cta-section">
                        <button class="primary-cta" onclick="openSubscriptionModal()">
                            <span class="cta-text">Start Your Journey</span>
                            <span class="cta-arrow">→</span>
                        </button>
                        <div class="pricing-preview">
                            <?php if (!empty($plans)): ?>
                                <span class="price">$<?php echo number_format($plans[0]->price, 2); ?></span>
                                <span class="period">/ <?php echo $plans[0]->duration_months; ?> months</span>
                                <span class="trial">• 7-day money back guarantee</span>
                            <?php else: ?>
                                <span class="price">$29.99</span>
                                <span class="period">/ 6 months</span>
                                <span class="trial">• 7-day money back guarantee</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Floating Elements -->
            <div class="floating-elements">
                <div class="float-element element-1">❤️'</div>
                <div class="float-element element-2">⚡</div>
                <!-- <div class="float-element element-3">🎨</div> -->
                <div class="float-element element-4">🦖</div>
            </div>
        </div>
    </section>

    <!-- Features Showcase -->
    <section class="features-showcase">
        <div class="features-container">
            <!-- Section Header -->
            <div class="section-header">
                <h2 class="section-title">
                    Everything You Need for the
                    <span class="gradient-text">Perfect Reading Experience</span>
                </h2>
            </div>

            <!-- Features Layout -->
            <div class="features-layout">
                <!-- Hero Feature -->
                <div class="hero-feature">
                    <div class="feature-icon-large"><img draggable="false" role="img" class="emoji" alt="⚡" src="https://s.w.org/images/core/emoji/16.0.1/svg/26a1.svg"></div>
                    <div class="hero-feature-content">
                        <h3>Unlimited Premium Access</h3>
                        <p>Read all premium manga chapters without any restrictions. No waiting, no limits.</p>
                        <div class="feature-stats">
                            <span class="stats-number">1000+</span>
                            <span class="stats-label">Premium Chapters</span>
                        </div>
                    </div>
                </div>

                <!-- Features Grid -->
                <div class="features-grid-new">
                    <div class="feature-item">
                        <div class="feature-icon-small"><img draggable="false" role="img" class="emoji" alt="📚" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4da.svg"></div>
                        <div class="feature-text">
                            <h4>Early Access</h4>
                            <p>Get new chapters 24-48 hours before free users.</p>
                        </div>
                    </div><div class="feature-item">
                        <div class="feature-icon-small"><img draggable="false" role="img" class="emoji" alt="👑" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f451.svg"></div>
                        <div class="feature-text">
                            <h4>Premium Badge &amp; Status</h4>
                            <p>Show off your premium status with exclusive name colors on comments and Discord.</p>
                            <div class="feature-badges">
                                <span class="badge">Premium Badge</span>
                                <span class="badge">Discord Status</span>
                                <span class="badge">Comment Colors</span>
                            </div>
                        </div>
                    </div>

                    

                    <div class="feature-item feature-wide">
                        <div class="feature-icon-small"><img draggable="false" role="img" class="emoji" alt="🌟" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f31f.svg"></div>
                        <div class="feature-text">
                            <h4>Ad-Free Experience</h4>
                            <p>Enjoy uninterrupted reading with zero advertisements and distractions.</p>
                            <div class="feature-badges">
                                <span class="badge">No Ads</span>
                                <span class="badge">Clean Interface</span>
                                <span class="badge">Focus Mode</span>
                            </div>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon-small"><img draggable="false" role="img" class="emoji" alt="💬" src="https://s.w.org/images/core/emoji/16.0.1/svg/1f4ac.svg"></div>
                        <div class="feature-text">
                            <h4>Priority Support</h4>
                            <p>Get faster response times and dedicated support.</p>
                        </div>
                    </div>

                    
                </div>
            </div>

            <!-- Popular Manga Showcase -->
            <div class="manga-showcase">
                <h3 class="showcase-subtitle">Featured Premium Series</h3>
                <div class="manga-carousel-container">
                    <div class="swiper manga-carousel-swiper">
                        <div class="swiper-wrapper">
                            <?php if (!empty($popular_manga)): ?>
                                <?php foreach (array_slice($popular_manga, 0, 12) as $index => $manga): ?>
                                    <div class="swiper-slide">
                                        <div class="manga-card" style="--delay: <?php echo $index * 0.1; ?>s">
                                            <a href="<?php echo get_permalink($manga->ID); ?>" class="manga-link">
                                                <div class="manga-cover">
                                                    <?php if (has_post_thumbnail($manga->ID)): ?>
                                                        <?php echo get_the_post_thumbnail($manga->ID, 'medium', array('class' => 'cover-image')); ?>
                                                    <?php else: ?>
                                                        <div class="cover-placeholder">
                                                            <span class="placeholder-icon">📖</span>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="premium-badge">PREMIUM</div>
                                                </div>
                                                <div class="manga-info">
                                                    <h4><?php echo esc_html($manga->post_title); ?></h4>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <!-- Placeholder manga cards -->
                                <?php for ($i = 0; $i < 12; $i++): ?>
                                    <div class="swiper-slide">
                                        <div class="manga-card" style="--delay: <?php echo $i * 0.1; ?>s">
                                            <div class="manga-cover">
                                                <div class="cover-placeholder">
                                                    <span class="placeholder-icon">📖</span>
                                                </div>
                                                <div class="premium-badge">PREMIUM</div>
                                            </div>
                                            <div class="manga-info">
                                                <h4>Premium Series <?php echo $i + 1; ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            <?php endif; ?>
                        </div>

                        <!-- Navigation buttons -->
                        <div class="swiper-button-next manga-carousel-next"></div>
                        <div class="swiper-button-prev manga-carousel-prev"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="bg-elements">
                <div class="pattern-image case-2"></div>
            </div>
        <div class="faq-container">
            <div class="section-header">
                <h2 class="section-title">
                    Got Questions?
                    <span class="gradient-text">We've Got Answers</span>
                </h2>
            </div>

            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question">
                        <span class="faq-icon">💳</span>
                        <h3>Can I cancel my subscription anytime?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Absolutely! You can cancel your subscription at any time with just one click. You'll continue to have access until the end of your current billing period.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span class="faq-icon">🔒</span>
                        <h3>What payment methods do you accept?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>We accept PayPal and all major credit cards through our secure payment processor. Your payment information is always encrypted and protected.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span class="faq-icon">↩️</span>
                        <h3>Do you offer refunds?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Yes! We offer a 7-day money-back guarantee. If you're not completely satisfied with your subscription, we'll refund your payment, no questions asked.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <span class="faq-icon">📱</span>
                        <h3>Can I read on multiple devices?</h3>
                    </div>
                    <div class="faq-answer">
                        <p>Of course! Your subscription works on all your devices - phone, tablet, computer. Your reading progress syncs automatically across all devices.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
            <div class="bg-elements">
                <div class="pattern-image case-1"></div>
            </div>
        <div class="cta-container">
            <!-- Background Elements -->
            <div class="cta-content">
                <?php if (!$has_active_subscription): ?>
                    <!-- Non-subscriber CTA -->
                    <div class="cta-main">
                        <div class="cta-badge">
                            <span class="badge-icon">🚀</span>
                            <span class="badge-text">READY TO START?</span>
                        </div>

                        <h2 class="cta-title">
                            Join the Premium
                            <span class="gradient-text">Reading Revolution</span>
                        </h2>

                        <p class="cta-subtitle">
                            Don't let premium content slip away. Join thousands of readers who've already upgraded their manga experience.
                        </p>

                        <div class="cta-features-mini">
                            <div class="mini-feature">
                                <span class="mini-icon">⚡</span>
                                <span>Instant Access</span>
                            </div>
                            <div class="mini-feature">
                                <span class="mini-icon">🔒</span>
                                <span>Secure Payment</span>
                            </div>
                            <div class="mini-feature">
                                <span class="mini-icon">↩️</span>
                                <span>Money Back Guarantee</span>
                            </div>
                        </div>

                        <div class="cta-action">
                            <button class="mega-cta" onclick="openSubscriptionModal()">
                                <span class="cta-text">Start Your Premium Journey</span>
                                <span class="cta-subtext">
                                    <?php if (!empty($plans)): ?>
                                        Starting at $<?php echo number_format($plans[0]->price, 2); ?> / <?php echo $plans[0]->duration_months; ?> months
                                    <?php else: ?>
                                        Starting at $29.99 / 6 months
                                    <?php endif; ?>
                                </span>
                                <span class="cta-arrow">→</span>
                            </button>

                            <div class="cta-guarantee">
                                <span class="guarantee-icon">🛡️</span>
                                <span>7-day money back guarantee • Cancel anytime</span>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Premium member status -->
                    <div class="premium-member-cta">
                        <div class="member-crown-large">👑</div>
                        <h2 class="member-title">
                            Welcome Back,
                            <span class="gradient-text">Premium Member!</span>
                        </h2>
                        <p class="member-subtitle">
                            You're part of an exclusive community enjoying the best manga experience.
                        </p>

                        <div class="member-status-card">
                            <div class="status-info">
                                <div class="status-item">
                                    <span class="status-label">Current Plan</span>
                                    <span class="status-value"><?php echo esc_html($current_subscription->plan_name); ?></span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Expires</span>
                                    <span class="status-value"><?php echo date('M j, Y', strtotime($current_subscription->end_date)); ?></span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Status</span>
                                    <span class="status-value active">Active</span>
                                </div>
                            </div>
                        </div>

                        <div class="member-actions">
                            <button class="action-btn secondary" onclick="cancelSubscription()">
                                <span>Manage Subscription</span>
                            </button>
                            <button class="action-btn primary" onclick="openSubscriptionModal()">
                                <span>Upgrade Plan</span>
                                <span class="btn-arrow">→</span>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
</div>

<style>
/* Ultra-Modern Subscription Page */
.entry-header {
    display: none !important;
}

.ultra-modern-subscription {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* Particle Background */
.particle-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: linear-gradient(45deg, #ff6b35, #ff8e53);
    border-radius: 50%;
    animation: float-particle 20s infinite linear;
}

.particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 25s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 5s;
    animation-duration: 30s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 10s;
    animation-duration: 35s;
}

.particle:nth-child(4) {
    top: 40%;
    left: 60%;
    animation-delay: 15s;
    animation-duration: 28s;
}

.particle:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: 20s;
    animation-duration: 32s;
}

@keyframes float-particle {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    z-index: 1;
    padding: 60px 0;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

/* Background Elements */
.bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.pattern-image {
    position: absolute;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.6;
    filter: blur(8px);
    animation: float-pattern 15s ease-in-out infinite;
}

.pattern-1 {
    width: 300px;
    height: 300px;
    background-image: url('/wp-content/plugins/wp-manga-chapter-coin/assets/images/pattern-1731-1.png');
    top: 15%;
    left: 10%;
    animation-delay: 0s;
    transform: translateX(-20%) translateY(75%);
}

.pattern-2 {
    width: 420px;
    height: 500px;
    background-image: url('/wp-content/plugins/wp-manga-chapter-coin/assets/images/subscribe.png');
    top: 50%;
    right: 0;
    animation-delay: 3s;
    transform: translateX(50%) translateY(25%);
}

.case-1 {
    width: 300px;
    height: 300px;
    background-image: url('/wp-content/plugins/wp-manga-chapter-coin/assets/images/case-1.png');
    top: 0;
    left: 15%;
    animation-delay: 0s;
    transform: translateX(-20%) translateY(75%);
}
.case-2 {
    width: 300px;
    height: 300px;
    background-image: url('/wp-content/plugins/wp-manga-chapter-coin/assets/images/case-2.png');
    top: 70%;
    right: 5%;
    animation-delay: 0s;
    transform: translateX(-20%) translateY(75%);
}

@keyframes float-pattern {
    0%, 100% { transform: translateX(-20%) translateY(75%) scale(1); }
    50% { transform: translateX(-10%) translateY(65%) scale(1.05); }
}

.pattern-2 {
    animation-name: float-pattern-2;
}

@keyframes float-pattern-2 {
    0%, 100% { transform: translateX(50%) translateY(25%) scale(1); }
    50% { transform: translateX(40%) translateY(15%) scale(1.05); }
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Hero Content */
.hero-content {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.brand-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 50px;
    padding: 6px 16px;
    margin-bottom: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.brand-icon {
    font-size: 16px;
}

.brand-text {
    color: white;
}

.brand-plus {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-title {
    font-size: clamp(2.5rem, 7vw, 4.5rem);
    font-weight: 900;
    line-height: 0.95;
    margin: 0 0 20px 0;
    letter-spacing: -1px;
}

.title-line-1, .title-line-3 {
    display: block;
    color: #ffffff;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.title-line-2 {
    display: block;
    margin: 8px 0;
}

.gradient-word {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.title-word {
    color: #ffffff;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.hero-subtitle {
    font-size: 1.1rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 32px 0;
    max-width: 550px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    margin: 0 0 32px 0;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    background: transparent;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-divider {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
}

/* CTA Section */
.cta-section {
    margin-top: 32px;
}

.primary-cta {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border: none;
    border-radius: 12px;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 24px rgba(255, 107, 53, 0.4);
    position: relative;
    overflow: hidden;
}

.primary-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.primary-cta:hover::before {
    left: 100%;
}

.primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(255, 107, 53, 0.5);
}

.cta-arrow {
    font-size: 1.5rem;
    transition: transform 0.3s;
}

.primary-cta:hover .cta-arrow {
    transform: translateX(4px);
}

.pricing-preview {
    margin-top: 16px;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.price {
    font-weight: 700;
    color: #ff6b35;
    font-size: 1.1rem;
}

.period {
    margin-left: 4px;
}

.trial {
    margin-left: 8px;
    font-size: 0.9rem;
}

/* Premium Member Display */
.premium-member-display {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 20px;
    padding: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    max-width: 500px;
    margin: 0 auto;
}

.member-crown {
    font-size: 3rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.member-info h3 {
    margin: 0 0 8px 0;
    color: #10B981;
    font-size: 1.5rem;
    text-align: center;
}

.member-info p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
}

.manage-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 24px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.manage-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.float-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.6;
    animation: float-element 15s infinite ease-in-out;
}

.element-1 {
    top: 10%;
    left: 15%;
    animation-delay: 0s;
}

.element-2 {
    top: 30%;
    right: 7%;
    animation-delay: 3s;
}

.element-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 6s;
}

.element-4 {
    bottom: 20%;
    right: 15%;
    animation-delay: 9s;
}

@keyframes float-element {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

/* Features Showcase Section */
.features-showcase {
    padding: 0px 0;
    position: relative;
    z-index: 1;
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 50px;
    padding: 6px 16px;
    margin-bottom: 16px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.8);
}

.badge-icon {
    font-size: 14px;
}

.section-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 900;
    line-height: 1.2;
    margin: 0 0 16px 0;
    color: white;
    letter-spacing: -0.5px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.gradient-text {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Layout */
.features-layout {
    max-width: 1000px;
    margin: 0 auto 50px auto;
}

/* Hero Feature */
.hero-feature {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
}

.hero-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 142, 83, 0.05) 100%);
    opacity: 0.8;
}

.feature-icon-large {
    font-size: 4rem;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.hero-feature-content {
    position: relative;
    z-index: 1;
}

.hero-feature h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 16px 0;
    color: white;
}

.hero-feature p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 24px 0;
    line-height: 1.6;
}

.feature-stats {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 8px;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stats-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Features Grid */
.features-grid-new {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.features-grid-new .feature-item:nth-child(5) {
    grid-column: 1 / -1;
    max-width: 600px;
    margin: 0 auto;
}

.feature-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 107, 53, 0.3);
    transform: translateY(-2px);
}

.feature-wide {
    grid-column: 1 / -1;
}

.feature-icon-small {
    font-size: 1.8rem;
    flex-shrink: 0;
    margin-top: 4px;
}

.feature-text h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: white;
}

.feature-text p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.feature-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.badge {
    background: rgba(255, 107, 53, 0.2);
    border: 1px solid rgba(255, 107, 53, 0.3);
    color: #ff8e53;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 142, 83, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s;
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: rgba(255, 107, 53, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card-large {
    grid-column: span 2;
}

.card-wide {
    grid-column: span 2;
}

.feature-icon {
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.icon-bg {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
}

.feature-content {
    position: relative;
    z-index: 1;
}

.feature-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 12px 0;
    color: white;
}

.feature-content p {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin: 0 0 20px 0;
}

.feature-highlight {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-top: 20px;
}

.highlight-number {
    font-size: 2rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.highlight-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.feature-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 16px;
}

.tag {
    background: rgba(255, 107, 53, 0.2);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 20px;
    padding: 4px 12px;
    font-size: 0.8rem;
    color: #ff8e53;
    font-weight: 600;
}

.feature-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.feature-card:hover .feature-glow {
    opacity: 1;
}

/* Genre Slider Section */
.genre-slider-section {
    padding: 80px 0;
    overflow: hidden;
    position: relative;
}

.genre-slider {
    position: relative;
    overflow: hidden;
}

.genre-track {
    display: flex;
    animation: scroll 30s linear infinite;
    gap: 40px;
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

.genre-item {
    display: flex;
    align-items: center;
    gap: 40px;
    flex-shrink: 0;
}

.genre-text {
    font-size: 2.5rem;
    font-weight: 800;
    color: rgba(255, 255, 255, 0.4);
    letter-spacing: 2px;
    white-space: nowrap;
}

.genre-star {
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.4);
}

/* Final CTA Section */
.final-cta-section {
    position: relative;
    padding: 120px 0;
    overflow: hidden;
}

.cta-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.cta-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 20px 0;
}

.cta-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin: 0 0 60px 0;
    letter-spacing: 3px;
    text-transform: uppercase;
    color: #F8F8F8;
}

.cta-subscribe-btn {
    background: linear-gradient(135deg, #E33256 0%, #F63943 100%);
    border: none;
    padding: 16px 64px;
    border-radius: 50px;
    color: white;
    font-size: 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 8px 32px rgba(227, 50, 86, 0.4);
    margin-bottom: 12px;
}

.cta-subscribe-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(227, 50, 86, 0.5);
}

.cta-pricing {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.premium-member-status {
    max-width: 600px;
    margin: 0 auto;
}

.member-badge {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid #10B981;
    border-radius: 20px;
    padding: 32px;
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 32px;
}

.member-info h3 {
    margin: 0 0 8px 0;
    color: #10B981;
    font-size: 1.5rem;
}

.member-info p {
    margin: 4px 0;
    color: #ccc;
    font-size: 1rem;
}

.member-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.btn-primary, .btn-secondary {
    padding: 14px 28px;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #E33256 0%, #F63943 100%);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-pattern {
    position: absolute;
    width: 420px;
    height: 500px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23E53355" opacity="0.3"/></svg>') repeat;
    opacity: 0.6;
    filter: blur(7px);
    pointer-events: none;
}

.cta-pattern.left {
    bottom: 0;
    left: 0;
    transform: translateX(-10%);
}

.cta-pattern.right {
    bottom: 0;
    right: 0;
    transform: translateX(5%);
}

/* Manga Showcase */
.manga-showcase {
    margin-top: 60px;
}

.showcase-subtitle {
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    margin: 0 0 40px 0;
    color: rgba(255, 255, 255, 0.9);
}

.manga-carousel-container {
    width: 100%;
    position: relative;
    margin: 0;
}

.manga-carousel-swiper {
    width: 100%;
    padding: 0 60px;
    overflow: hidden;
}

.manga-carousel-swiper .swiper-wrapper {
    align-items: stretch;
}

.manga-carousel-swiper .swiper-slide {
    height: auto;
    display: flex;
}

.manga-card {
    animation: fade-in-up 0.6s ease-out forwards;
    animation-delay: var(--delay);
    opacity: 0;
    transform: translateY(20px);
    width: 100%;
    height: 100%;
}

.manga-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
    transition: transform 0.3s ease;
}

.manga-link:hover {
    transform: translateY(-5px);
}

/* Swiper Navigation Buttons */
.manga-carousel-next,
.manga-carousel-prev {
    width: 40px;
    height: 40px;
    margin-top: -20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.manga-carousel-next:hover,
.manga-carousel-prev:hover {
    background: rgba(255, 107, 53, 0.8);
    transform: scale(1.1);
}

.manga-carousel-next::after,
.manga-carousel-prev::after {
    font-size: 16px;
    font-weight: 700;
    color: white;
}



@keyframes fade-in-up {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.manga-cover {
    position: relative;
    aspect-ratio: 3/4;
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    transition: transform 0.3s;
}

.manga-card:hover .manga-cover {
    transform: scale(1.05);
}

.cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cover-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
}

.placeholder-icon {
    font-size: 3rem;
    opacity: 0.5;
}

/*.premium-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}*/

.manga-info {
    padding: 16px 0;
    text-align: center;
}

.manga-info h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: white;
    line-height: 1.3;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-container,
    .features-container {
        padding: 0 20px;
    }

    .card-large,
    .card-wide {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 70vh;
        padding: 40px 0;
    }

    .hero-title {
        font-size: clamp(1.8rem, 7vw, 2.5rem);
        letter-spacing: -0.5px;
    }

    .hero-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .primary-cta {
        padding: 16px 32px;
        font-size: 1.1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card {
        padding: 24px;
    }

    .manga-carousel-swiper {
        padding: 0 40px;
    }

    .manga-carousel-next,
    .manga-carousel-prev {
        width: 35px;
        height: 35px;
        margin-top: -17.5px;
    }

    .floating-elements {
        display: none;
    }

    .premium-member-display {
        padding: 24px;
    }

    .member-crown {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-container,
    .features-container {
        padding: 0 16px;
    }

    .hero-title {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 16px;
    }

    .stat-divider {
        display: none;
    }

    .primary-cta {
        padding: 14px 28px;
        font-size: 1rem;
    }

    .section-title {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
    }

    .manga-carousel-swiper {
        padding: 0 25px;
    }

    .manga-carousel-next,
    .manga-carousel-prev {
        width: 30px;
        height: 30px;
        margin-top: -15px;
    }

    .manga-carousel-next::after,
    .manga-carousel-prev::after {
        font-size: 14px;
    }
}

/* Pricing Section */
.ultra-modern-subscription .pricing-section {
    padding: 80px 0;
    position: relative;
    z-index: 1;
}

.ultra-modern-subscription .pricing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.ultra-modern-subscription .pricing-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    max-width: 1000px;
    margin: 0 auto 40px auto;
}

.ultra-modern-subscription .pricing-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 28px 24px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.ultra-modern-subscription .pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 142, 83, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s;
}

.ultra-modern-subscription .pricing-card:hover::before {
    opacity: 1;
}

.ultra-modern-subscription .pricing-card:hover {
    transform: translateY(-12px);
    border-color: rgba(255, 107, 53, 0.4);
    box-shadow: 0 24px 48px rgba(0, 0, 0, 0.4);
}

.ultra-modern-subscription .pricing-card.popular {
    border-color: rgba(255, 107, 53, 0.5);
    transform: scale(1.05);
}

.ultra-modern-subscription .pricing-card.popular:hover {
    transform: scale(1.05) translateY(-12px);
}

.ultra-modern-subscription .pricing-card.best-value {
    border-color: rgba(255, 142, 83, 0.5);
}

.ultra-modern-subscription .card-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.ultra-modern-subscription .popular-badge {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4);
}

.ultra-modern-subscription .value-badge {
    background: linear-gradient(135deg, #ff8e53 0%, #ffab70 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(255, 142, 83, 0.4);
}

.ultra-modern-subscription .card-header {
    text-align: center;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
}

.ultra-modern-subscription .plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 20px 0;
    color: white;
}

.ultra-modern-subscription .plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
    margin-bottom: 8px;
}

.ultra-modern-subscription .price-currency {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b35;
}

.ultra-modern-subscription .price-amount {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.ultra-modern-subscription .price-period {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

.ultra-modern-subscription .price-per-month {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 12px;
}

.ultra-modern-subscription .savings-badge {
    background: rgba(16, 185, 129, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.4);
    color: #10B981;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
}

.ultra-modern-subscription .card-features {
    margin-bottom: 32px;
    position: relative;
    z-index: 1;
}

.ultra-modern-subscription .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.ultra-modern-subscription .feature-icon {
    font-size: 1.2rem;
    color: #ff6b35;
}

.ultra-modern-subscription .plan-cta {
    width: 100%;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border: none;
    border-radius: 16px;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.ultra-modern-subscription .plan-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.ultra-modern-subscription .plan-cta:hover::before {
    left: 100%;
}

.ultra-modern-subscription .plan-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
}

.ultra-modern-subscription .cta-arrow {
    transition: transform 0.3s;
}

.ultra-modern-subscription .plan-cta:hover .cta-arrow {
    transform: translateX(4px);
}

.ultra-modern-subscription .trust-indicators {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
    margin-top: 40px;
}

.ultra-modern-subscription .trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-weight: 500;
}

.ultra-modern-subscription .trust-icon {
    font-size: 1.2rem;
    color: #ff6b35;
}

/* Social Proof Section */
.ultra-modern-subscription .social-proof {
    padding: 60px 0;
    background: rgba(255, 255, 255, 0.02);
}

.ultra-modern-subscription .proof-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

.ultra-modern-subscription .proof-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 60px;
    flex-wrap: wrap;
}

.ultra-modern-subscription .stat-card {
    text-align: center;
    padding: 24px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    min-width: 150px;
    transition: transform 0.3s;
}

.ultra-modern-subscription .stat-card:hover {
    transform: translateY(-4px);
}

.ultra-modern-subscription .stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    line-height: 1;
    margin-bottom: 8px;
}

.ultra-modern-subscription .stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    font-weight: 500;
}

.ultra-modern-subscription .testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
    max-width: 1000px;
    margin: 0 auto;
}

.ultra-modern-subscription .testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 32px;
    transition: transform 0.3s;
}

.ultra-modern-subscription .testimonial-card:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 107, 53, 0.3);
}

.ultra-modern-subscription .testimonial-content {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 24px;
    font-style: italic;
}

.ultra-modern-subscription .testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.ultra-modern-subscription .author-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.ultra-modern-subscription .author-name {
    font-weight: 600;
    color: white;
    margin-bottom: 4px;
}

.ultra-modern-subscription .author-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

/* FAQ Section */
.ultra-modern-subscription .faq-section {
    padding: 80px 0;
}

.ultra-modern-subscription .faq-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 24px;
}

.ultra-modern-subscription .faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 24px;
    margin-top: 60px;
}

.ultra-modern-subscription .faq-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    transition: all 0.3s;
}

.ultra-modern-subscription .faq-item:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 107, 53, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.ultra-modern-subscription .faq-question {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.ultra-modern-subscription .faq-icon {
    font-size: 1.5rem;
    color: #ff6b35;
    flex-shrink: 0;
    margin-top: 4px;
}

.ultra-modern-subscription .faq-question h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin: 0;
    line-height: 1.4;
}

.ultra-modern-subscription .faq-answer p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
}

/* Final CTA Section */
.ultra-modern-subscription .final-cta {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.ultra-modern-subscription .cta-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
    z-index: 2;
}

.ultra-modern-subscription .cta-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.ultra-modern-subscription .cta-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    animation: float-orb 10s ease-in-out infinite;
}

.ultra-modern-subscription .cta-orb.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.ultra-modern-subscription .cta-orb.orb-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(229, 51, 85, 0.2) 0%, transparent 70%);
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.ultra-modern-subscription .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 107, 53, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 107, 53, 0.05) 1px, transparent 1px);
    background-size: 60px 60px;
    opacity: 0.3;
    animation: grid-move 25s linear infinite;
}

.ultra-modern-subscription .cta-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.ultra-modern-subscription .cta-main {
    max-width: 700px;
    margin: 0 auto;
}

.ultra-modern-subscription .cta-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 900;
    line-height: 1.2;
    margin: 16px 0 24px 0;
    color: white;
    letter-spacing: -0.5px;
}

.ultra-modern-subscription .cta-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 40px;
}

.ultra-modern-subscription .cta-features-mini {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 32px;
    flex-wrap: wrap;
}

.ultra-modern-subscription .cta-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.ultra-modern-subscription .mini-feature {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-weight: 500;
}

.ultra-modern-subscription .mini-icon {
    font-size: 1.2rem;
    color: #ff6b35;
}

.ultra-modern-subscription .mega-cta {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border: none;
    border-radius: 16px;
    padding: 18px 36px;
    color: white;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.4);
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
}

.ultra-modern-subscription .mega-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.ultra-modern-subscription .mega-cta:hover::before {
    left: 100%;
}

.ultra-modern-subscription .mega-cta:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(255, 107, 53, 0.5);
}

.ultra-modern-subscription .cta-text {
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.ultra-modern-subscription .cta-subtext {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

.ultra-modern-subscription .cta-arrow {
    font-size: 1.5rem;
    transition: transform 0.3s;
}

.ultra-modern-subscription .mega-cta:hover .cta-arrow {
    transform: translateX(6px);
}

.ultra-modern-subscription .cta-guarantee {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.ultra-modern-subscription .guarantee-icon {
    color: #10B981;
}

/* Premium Member CTA */
.ultra-modern-subscription .premium-member-cta {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.ultra-modern-subscription .member-crown-large {
    font-size: 4rem;
    margin-bottom: 24px;
    animation: bounce 2s infinite;
}

.ultra-modern-subscription .member-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 900;
    line-height: 1.1;
    margin: 0 0 20px 0;
    color: white;
}

.ultra-modern-subscription .member-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
    line-height: 1.6;
}

.ultra-modern-subscription .member-status-card {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
}

.ultra-modern-subscription .status-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 24px;
}

.ultra-modern-subscription .status-item {
    text-align: center;
}

.ultra-modern-subscription .status-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.ultra-modern-subscription .status-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
}

.ultra-modern-subscription .status-value.active {
    color: #10B981;
}

.ultra-modern-subscription .member-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.ultra-modern-subscription .action-btn {
    padding: 14px 28px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ultra-modern-subscription .action-btn.primary {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
}

.ultra-modern-subscription .action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.ultra-modern-subscription .action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ultra-modern-subscription .action-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.ultra-modern-subscription .btn-arrow {
    transition: transform 0.3s;
}

.ultra-modern-subscription .action-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* Responsive Design - Scoped to subscription page only */
@media (max-width: 1200px) {
    .ultra-modern-subscription .features-grid-new {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .ultra-modern-subscription .faq-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .ultra-modern-subscription .features-grid-new {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .ultra-modern-subscription .hero-feature {
        padding: 32px 24px;
    }

    .ultra-modern-subscription .feature-icon-large {
        font-size: 3rem;
        margin-bottom: 16px;
    }

    .ultra-modern-subscription .hero-feature h3 {
        font-size: 1.5rem;
    }

    .ultra-modern-subscription .feature-item {
        padding: 20px;
    }

    .ultra-modern-subscription .cta-features-mini {
        flex-direction: column;
        gap: 16px;
    }

    .ultra-modern-subscription .mega-cta {
        padding: 20px 32px;
    }

    .ultra-modern-subscription .member-actions {
        flex-direction: column;
    }

    .ultra-modern-subscription .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .ultra-modern-subscription .faq-item {
        padding: 24px;
    }

    .ultra-modern-subscription .mega-cta {
        padding: 18px 24px;
    }

    .ultra-modern-subscription .cta-text {
        font-size: 1.1rem;
    }

    .ultra-modern-subscription .status-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

/* Scrollbar styling - scoped to subscription page */
.ultra-modern-subscription * {
    scrollbar-width: thin;
    scrollbar-color: #ff6b35 #1a1a1a;
}

.ultra-modern-subscription *::-webkit-scrollbar {
    width: 8px;
}

.ultra-modern-subscription *::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.ultra-modern-subscription *::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8e53 100%);
    border-radius: 4px;
}

.ultra-modern-subscription *::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff8e53 0%, #ff6b35 100%);
}
</style>

<script>
function cancelSubscription() {
    if (confirm('Are you sure you want to cancel your subscription? You will lose access to premium content at the end of your current billing period.')) {
        // AJAX call to cancel subscription
        const formData = new FormData();
        formData.append('action', 'cancel_subscription');
        formData.append('nonce', '<?php echo wp_create_nonce('cancel_subscription'); ?>');

        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Your subscription has been cancelled. You will retain access until the end of your current billing period.');
                location.reload();
            } else {
                alert('Error cancelling subscription: ' + (data.data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cancelling subscription. Please try again.');
        });
    }
}

// Initialize Featured Premium Series Carousel
document.addEventListener('DOMContentLoaded', function() {
    // Check if Swiper is available
    if (typeof Swiper !== 'undefined') {
        const mangaCarousel = new Swiper('.manga-carousel-swiper', {
            // Basic settings
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,

            // Autoplay settings - continuous sliding
            autoplay: {
                delay: 2500,
                disableOnInteraction: false,
                pauseOnMouseEnter: false,
            },

            // Navigation
            navigation: {
                nextEl: '.manga-carousel-next',
                prevEl: '.manga-carousel-prev',
            },

            // Responsive breakpoints
            breakpoints: {
                480: {
                    slidesPerView: 2,
                    spaceBetween: 16,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
                1024: {
                    slidesPerView: 4,
                    spaceBetween: 24,
                },
                1200: {
                    slidesPerView: 5,
                    spaceBetween: 24,
                }
            },

            // Effects
            effect: 'slide',
            speed: 600,

            // Additional options
            grabCursor: true,
            watchOverflow: true,

            // Accessibility
            a11y: {
                prevSlideMessage: 'Previous manga',
                nextSlideMessage: 'Next manga',
            }
        });
    } else {
        console.warn('Swiper library not found. Featured Premium Series carousel will not be initialized.');
    }
});
</script>
